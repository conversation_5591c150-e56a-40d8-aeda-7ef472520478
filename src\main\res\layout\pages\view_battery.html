<style>
  .battery-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border: 1px solid #4b5563;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    max-width: 260px;
    min-width: 240px;
  }

  .battery-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: #6b7280;
  }

  .battery-icon {
    width: 80px;
    height: 120px;
    margin: 0 auto 16px;
    position: relative;
    background: linear-gradient(to bottom, #4b5563 0%, #374151 50%, #1f2937 100%);
    border-radius: 8px;
    border: 2px solid #6b7280;
  }

  .battery-icon::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 6px;
    background: #6b7280;
    border-radius: 2px;
  }

  .battery-level {
    position: absolute;
    bottom: 4px;
    left: 4px;
    right: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .battery-level.high {
    background: linear-gradient(to top, #10b981, #34d399);
  }

  .battery-level.medium {
    background: linear-gradient(to top, #f59e0b, #fbbf24);
  }

  .battery-level.low {
    background: linear-gradient(to top, #ef4444, #f87171);
  }

  .battery-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .info-item {
    background: rgba(55, 65, 81, 0.5);
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #4b5563;
  }

  .info-label {
    font-size: 12px;
    color: #9ca3af;
    margin-bottom: 2px;
  }

  .info-value {
    font-size: 14px;
    font-weight: 600;
    color: #f3f4f6;
  }

  .serial-number {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #f3f4f6;
    margin-bottom: 16px;
    padding: 8px;
    background: rgba(55, 65, 81, 0.5);
    border-radius: 6px;
    border: 1px solid #4b5563;
  }

  .battery-percentage {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
  }

  .details-btn {
    width: 100%;
    padding: 8px 16px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .details-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
  }

  .no-battery {
    opacity: 0.5;
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  }

  .no-battery .battery-icon {
    background: linear-gradient(to bottom, #6b7280 0%, #4b5563 50%, #374151 100%);
    border-color: #9ca3af;
  }

  .no-battery .battery-icon::before {
    background: #9ca3af;
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .detail-section {
    background: rgba(55, 65, 81, 0.5);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #4b5563;
  }

  .detail-section h4 {
    color: #f3f4f6;
    font-weight: 600;
    margin-bottom: 12px;
    border-bottom: 1px solid #4b5563;
    padding-bottom: 8px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 4px 0;
  }

  .detail-label {
    color: #9ca3af;
    font-size: 14px;
  }

  .detail-value {
    color: #f3f4f6;
    font-weight: 500;
    font-size: 14px;
  }
</style>

<!-- 電池檢視頁面 -->
<div class="container mx-auto p-6">
  <!-- 統計卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="stat-card">
      <div class="flex justify-between items-center">
        <div>
          <div class="text-sm text-gray-400">總電池數</div>
          <div id="total-batteries" class="text-2xl font-bold">0</div>
        </div>
        <div class="text-accent-yellow">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 7H7v6h6V7z"></path>
            <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7.586 5H7a3 3 0 00-3 3v8a3 3 0 003 3h6a3 3 0 003-3V8a3 3 0 00-3-3h-.586l1.293-1.293A1 1 0 0016 2H7zm6 5a1 1 0 011 1v8a1 1 0 01-1 1H7a1 1 0 01-1-1V8a1 1 0 011-1h6z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="flex justify-between items-center">
        <div>
          <div class="text-sm text-gray-400">正常電池</div>
          <div id="normal-batteries" class="text-2xl font-bold">0</div>
        </div>
        <div class="text-green-500">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="flex justify-between items-center">
        <div>
          <div class="text-sm text-gray-400">充電中</div>
          <div id="charging-batteries" class="text-2xl font-bold">0</div>
        </div>
        <div class="text-blue-500">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M11 3a1 1 0 10-2 0v1.188C7.09 4.938 6 6.7 6 8.5 6 10.433 7.33 12 9 12c.685 0 1.216-.138 1.627-.361.154-.083.34-.17.51-.245.886-.393 1.37-.788 1.784-1.225.415-.437.79-.924 1.067-1.483.276-.56.412-1.17.412-1.822 0-1.638-1.04-2.812-2.4-2.864V3zm-6 5.5c0-2.39 1.5-4.5 4-5.41V2a3 3 0 016 0v1.09c2.5.91 4 3.01 4 5.41 0 2.48-1.5 4.5-4 5.41v1.09a3 3 0 01-6 0v-1.09c-2.5-.91-4-3.01-4-5.41z"></path>
          </svg>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="flex justify-between items-center">
        <div>
          <div class="text-sm text-gray-400">低電量</div>
          <div id="low-batteries" class="text-2xl font-bold">0</div>
        </div>
        <div class="text-yellow-500">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- 電池網格顯示 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="battery-grid">
    <!-- 電池卡片將由 JavaScript 動態生成 -->
  </div>

  <!-- 電池詳細資訊彈窗 -->
  <div id="battery-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-gray-800 p-6 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-bold text-gray-50">電池詳細資訊</h3>
        <button id="close-details-btn" class="text-gray-400 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div id="battery-details-content">
        <!-- 詳細資訊內容將由 JavaScript 填入 -->
      </div>
    </div>
  </div>
</div>



<!-- <script type="module">
  import { get_batteries } from '../../../core/scripts/pages/BatteryView.ts';
  
  // 初始化電池檢視
  document.addEventListener('DOMContentLoaded', () => {
    console.log('電池檢視頁面初始化');
    get_batteries();
  });
</script> -->